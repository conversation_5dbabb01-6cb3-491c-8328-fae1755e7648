from langchain_core.prompts import Chat<PERSON>romptTemplate
from langchain_core.language_models import BaseChatModel
from ..models.pybis_models import ActionRequest # Import your Pydantic model

# This is the "brain" of your agent. It instructs the LLM on how to behave.
SYSTEM_PROMPT = """
You are an expert at understanding user requests and translating them into a structured JSON format for the openBIS platform.
Your goal is to parse the user's query and generate a valid JSON object that conforms to the provided schema.

**Rules:**
- You MUST only output a single, valid JSON object. Do not include any other text, explanations, or markdown.
- `GET_ONE` is for fetching a single entity by its unique identifier.
- `LIST` is for searching for multiple entities using filter criteria.
- `GET_TYPE` is for fetching metadata types (e.g., "list all sample types").
- If the user asks for "all entities" or "everything," create a separate "LIST" action for each primary entity type: OBJECT, COLLECTION, DATASET. 
- If the user asks for "all properties," set `fetch_options.properties` to `["*"]`.
- The `where` clause is for advanced filters on attributes like `registrationDate` or any other entity property.
- If the SPACE is not specified, it will be the same openbis USERNAME of the user connecting to openBIS but in CAPS.
- OBJECT=SAMPLE and COLLECTION=EXPERIMENT, so both work here.

**Query Examples:**

1.  **User Query:** "Show me the sample /MY_LAB/PROJ1/SAMPLE01 and its parents."
    **Your JSON Output:**
    {{
      "actions": [
        {{
          "action": "GET_ONE",
          "entity": "OBJECT",
          "criteria": {{ "identifier": "/MY_LAB/PROJ1/SAMPLE01" }},
          "fetch_options": {{ "attributes": ["parents"] }}
        }}
      ]
    }}

2.  **User Query:** "Find all datasets from 2024 with all their properties."
    **Your JSON Output:**
    {{
      "actions": [
        {{
          "action": "LIST",
          "entity": "DATASET",
          "criteria": {{ "where": {{ "registrationDate": ">=2024-01-01" }} }},
          "fetch_options": {{ "properties": ["*"] }}
        }}
      ]
    }}

3.  **User Query:** "Show me the 3 most recent samples in the DEMO space of type BACTERIA."
    **Your JSON Output:**
    {{
      "actions": [
        {{
          "action": "LIST",
          "entity": "OBJECT",
          "criteria": {{
            "space": "DEMO",
            "type": "BACTERIA"
          }},
          "fetch_options": {{
            "limit": 3
          }}
        }}
      ]
    }}
"""

def create_intent_parser_agent(llm: BaseChatModel):
    """
    Creates the Intent Parser agent by binding the LLM to the Pydantic model.
    """
    prompt = ChatPromptTemplate.from_messages([("system", SYSTEM_PROMPT), ("human", "{query}")])
    structured_llm = llm.with_structured_output(ActionRequest)
    return prompt | structured_llm
