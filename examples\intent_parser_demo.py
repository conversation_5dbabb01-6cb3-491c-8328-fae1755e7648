#!/usr/bin/env python3
"""
Demonstration of the IntentParserAgent integration in chatBIS.

This script shows how the new IntentParserAgent works within the multi-agent system.
When users ask pybis-related queries, the system now:
1. Routes the query to the IntentParserAgent
2. Generates structured JSON representing the user's intent
3. Prints the JSON to console for verification
4. Terminates (no actual pybis execution in this phase)
"""

import sys
import os
import logging

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from chatBIS.query.conversation_engine import ConversationEngine

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def demonstrate_intent_parser():
    """Demonstrate the IntentParserAgent functionality."""
    
    print("="*70)
    print("CHATBIS INTENTPARSERAGENT DEMONSTRATION")
    print("="*70)
    print()
    print("This demo shows the new IntentParserAgent in action.")
    print("Function call queries are now converted to structured JSON.")
    print()
    
    try:
        # Initialize the conversation engine
        conversation_engine = ConversationEngine(
            data_dir="../data/processed",
            model="qwen3",
            memory_db_path="demo_memory.db"
        )
        
        # Create a session
        session_id = conversation_engine.create_session()
        
        # Example queries that demonstrate the IntentParserAgent
        demo_queries = [
            {
                "query": "Show me the 3 most recent samples in the DEMO space of type BACTERIA",
                "description": "Complex query with space, type, and limit filters"
            },
            {
                "query": "Get the sample /MY_LAB/PROJ1/SAMPLE01 and its parents",
                "description": "GET_ONE query with specific identifier and attributes"
            },
            {
                "query": "List all datasets from 2024 with all their properties",
                "description": "LIST query with date filter and all properties"
            },
            {
                "query": "What is openBIS?",
                "description": "Documentation query (should use RAG, not IntentParser)"
            }
        ]
        
        for i, demo in enumerate(demo_queries, 1):
            print(f"DEMO {i}: {demo['description']}")
            print(f"Query: \"{demo['query']}\"")
            print("-" * 50)
            
            try:
                response, session_id, metadata = conversation_engine.chat(demo['query'], session_id)
                
                decision = metadata.get('decision', 'unknown')
                print(f"Router Decision: {decision}")
                
                if decision == 'function_call':
                    print("✅ Successfully routed to IntentParserAgent!")
                    print("📋 Structured JSON output printed above.")
                elif decision == 'rag':
                    print("✅ Successfully routed to RAG agent for documentation.")
                    print(f"📖 Response: {response[:100]}...")
                else:
                    print(f"⚠️  Unexpected decision: {decision}")
                
            except Exception as e:
                print(f"❌ Error: {e}")
            
            print()
        
        print("="*70)
        print("DEMONSTRATION COMPLETE")
        print("="*70)
        print()
        print("Key observations:")
        print("• Function call queries → IntentParserAgent → Structured JSON")
        print("• Documentation queries → RAG agent → Natural language response")
        print("• The graph terminates after IntentParserAgent (no pybis execution)")
        print("• JSON format matches the expected ActionRequest schema")
        
        return 0
        
    except Exception as e:
        logger.error(f"Demo failed: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(demonstrate_intent_parser())
