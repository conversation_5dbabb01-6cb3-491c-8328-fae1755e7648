{
  "action": "GET_ONE" | "LIST" | "GET_TYPE",
  "entity": "OBJECT" | "COLLECTION" | "DATASET" | "PROJECT" | "SPACE" | "PROPERTY_TYPE" | "SAMPLE_TYPE" | "PROJECT_TYPE" | "VOCABULARY",
  "criteria": {
    // For GET_ONE: a unique identifier is required.
    "identifier": "/MY_SPACE/MY_PROJECT/MY_SAMPLE_01", // or a permId

    // For LIST: a combination of search parameters.
    "space": "MY_SPACE",
    "project": "MY_PROJECT",
    "collection": "MY_COLLECTION",
    "type": "YEAST",
    "withParents": ["/MY_SPACE/PARENT_SAMPLE"],
    "where": {
      "registrationDate": ">2024-01-01",
      "$NAME": "*human*",
      "NOTES": "Created from chatBIS"
    }
  },
  "fetch_options": {
    // Controls what and how much data is returned.
    "properties": ["$NAME", "DESCRIPTION"], // Maps to the `props` argument
    "attributes": ["parents", "children", "registrator.email"], // Maps to the `attrs` argument
    "limit": 10 // Could map to `count` argument
  }
}